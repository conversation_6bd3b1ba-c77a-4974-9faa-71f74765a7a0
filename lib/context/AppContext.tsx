'use client'

import React, { createContext, useContext, useReducer, useEffect } from 'react'
import { User, UserType } from '@/lib/types'

interface AppState {
  currentUser: User | null
  isLoading: boolean
}

type AppAction =
  | { type: 'SET_CURRENT_USER'; payload: User | null }
  | { type: 'SET_LOADING'; payload: boolean }

const initialState: AppState = {
  currentUser: null,
  isLoading: false,
}

function appReducer(state: AppState, action: AppAction): AppState {
  switch (action.type) {
    case 'SET_CURRENT_USER':
      return { ...state, currentUser: action.payload }
    case 'SET_LOADING':
      return { ...state, isLoading: action.payload }
    default:
      return state
  }
}

const AppContext = createContext<{
  state: AppState
  login: (userType: UserType, userData?: Partial<User>) => Promise<void>
  logout: () => void
} | null>(null)

export function AppProvider({ children }: { children: React.ReactNode }) {
  const [state, dispatch] = useReducer(appReducer, initialState)

  // Load data from localStorage on mount
  useEffect(() => {
    const savedUser = localStorage.getItem('currentUser')
    if (savedUser) {
      try {
        dispatch({ type: 'SET_CURRENT_USER', payload: JSON.parse(savedUser) })
      } catch (error) {
        console.error('Failed to parse saved user data:', error)
        localStorage.removeItem('currentUser')
      }
    }
  }, [])

  // Save current user to localStorage
  useEffect(() => {
    if (state.currentUser) {
      localStorage.setItem('currentUser', JSON.stringify(state.currentUser))
    } else {
      localStorage.removeItem('currentUser')
    }
  }, [state.currentUser])

  const login = async (userType: UserType, userData?: Partial<User>) => {
    dispatch({ type: 'SET_LOADING', payload: true })

    try {
      // Simulate authentication delay
      await new Promise(resolve => setTimeout(resolve, 300))

      const user: User = {
        id: userData?.id || `${userType}_${Date.now()}`,
        name: userData?.name || `${userType.charAt(0).toUpperCase() + userType.slice(1)} User`,
        email: userData?.email || `${userType}@example.com`,
        type: userType,
        company: userData?.company,
        phone: userData?.phone,
        location: userData?.location,
        reliabilityScore: userData?.reliabilityScore || 4.5,
        isActive: true,
        createdAt: new Date(),
        ...userData,
      }

      dispatch({ type: 'SET_CURRENT_USER', payload: user })
    } catch (error) {
      console.error('Login failed:', error)
      throw error
    } finally {
      dispatch({ type: 'SET_LOADING', payload: false })
    }
  }

  const logout = () => {
    dispatch({ type: 'SET_CURRENT_USER', payload: null })
  }

  return (
    <AppContext.Provider value={{ state, login, logout }}>
      {children}
    </AppContext.Provider>
  )
}

export function useApp() {
  const context = useContext(AppContext)
  if (!context) {
    throw new Error('useApp must be used within an AppProvider')
  }
  return context
}
