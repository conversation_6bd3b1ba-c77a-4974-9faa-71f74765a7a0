import { type ClassValue, clsx } from "clsx"
import { twMerge } from "tailwind-merge"
import { User, UserType } from './types'

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

export function formatCurrency(amount: number): string {
  return new Intl.NumberFormat('en-IN', {
    style: 'currency',
    currency: 'INR',
    minimumFractionDigits: 0,
  }).format(amount)
}

export function formatDate(date: Date): string {
  return new Intl.DateTimeFormat('en-IN', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
  }).format(date)
}

export function formatTime(date: Date): string {
  return new Intl.DateTimeFormat('en-IN', {
    hour: '2-digit',
    minute: '2-digit',
    hour12: true,
  }).format(date)
}

export function formatDateTime(date: Date): string {
  return `${formatDate(date)} at ${formatTime(date)}`
}

export function generateId(): string {
  return Math.random().toString(36).substr(2, 9)
}

// Authentication utilities for localStorage management
export const auth = {
  // Get current user from localStorage
  getCurrentUser(): User | null {
    if (typeof window === 'undefined') return null

    try {
      const savedUser = localStorage.getItem('currentUser')
      return savedUser ? JSON.parse(savedUser) : null
    } catch (error) {
      console.error('Failed to parse saved user data:', error)
      localStorage.removeItem('currentUser')
      return null
    }
  },

  // Save user to localStorage
  setCurrentUser(user: User | null): void {
    if (typeof window === 'undefined') return

    if (user) {
      localStorage.setItem('currentUser', JSON.stringify(user))
    } else {
      localStorage.removeItem('currentUser')
    }
  },

  // Login function
  async login(userType: UserType, userData?: Partial<User>): Promise<User> {
    // Simulate authentication delay
    await new Promise(resolve => setTimeout(resolve, 300))

    const user: User = {
      id: userData?.id || `${userType}_${Date.now()}`,
      name: userData?.name || `${userType.charAt(0).toUpperCase() + userType.slice(1)} User`,
      email: userData?.email || `${userType}@example.com`,
      type: userType,
      company: userData?.company,
      phone: userData?.phone,
      location: userData?.location,
      reliabilityScore: userData?.reliabilityScore || 4.5,
      isActive: true,
      createdAt: new Date(),
      ...userData,
    }

    this.setCurrentUser(user)
    return user
  },

  // Logout function
  logout(): void {
    this.setCurrentUser(null)
  },

  // Check if user is authenticated
  isAuthenticated(): boolean {
    return this.getCurrentUser() !== null
  },

  // Check if user has specific type
  hasUserType(userType: UserType): boolean {
    const user = this.getCurrentUser()
    return user?.type === userType
  }
}
