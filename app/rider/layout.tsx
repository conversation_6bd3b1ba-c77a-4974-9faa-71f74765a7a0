'use client'

import { useEffect, useState } from 'react'
import { useRouter } from 'next/navigation'
import { auth } from '@/lib/utils'
import DashboardLayout from '@/components/layout/DashboardLayout'

export default function RiderLayout({
  children,
}: {
  children: React.ReactNode
}) {
  const router = useRouter()
  const [currentUser, setCurrentUser] = useState(auth.getCurrentUser())
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    const user = auth.getCurrentUser()
    setCurrentUser(user)
    setIsLoading(false)

    if (!user) {
      router.push('/')
    } else if (user.type !== 'rider') {
      // Redirect to appropriate dashboard
      router.push(`/${user.type}/dashboard`)
    }
  }, [router])

  if (isLoading || !currentUser || currentUser.type !== 'rider') {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
          <p className="mt-2 text-muted-foreground">Loading...</p>
        </div>
      </div>
    )
  }

  return (
    <DashboardLayout userType="rider">
      {children}
    </DashboardLayout>
  )
}
