@import "tailwindcss";

:root {
  --background: 0 0% 100%; /* #FFFFFF */
  --foreground: 222.2 84% 4.9%; /* #020817 */
  --card: 0 0% 100%; /* #FFFFFF */
  --card-foreground: 222.2 84% 4.9%; /* #020817 */
  --popover: 0 0% 100%; /* #FFFFFF */
  --popover-foreground: 222.2 84% 4.9%; /* #020817 */
  --primary: 221.2 83.2% 53.3%; /* #3B82F6 */
  --primary-foreground: 210 40% 98%; /* #F8FAFC */
  --secondary: 210 40% 96%; /* #F1F5F9 */
  --secondary-foreground: 222.2 84% 4.9%; /* #020817 */
  --muted: 210 40% 96%; /* #F1F5F9 */
  --muted-foreground: 215.4 16.3% 46.9%; /* #64748B */
  --accent: 210 40% 96%; /* #F1F5F9 */
  --accent-foreground: 222.2 84% 4.9%; /* #020817 */
  --destructive: 0 84.2% 60.2%; /* #EF4444 */
  --destructive-foreground: 210 40% 98%; /* #F8FAFC */
  --border: 214.3 31.8% 91.4%; /* #E2E8F0 */
  --input: 214.3 31.8% 91.4%; /* #E2E8F0 */
  --ring: 221.2 83.2% 53.3%; /* #3B82F6 */
  --radius: 0.5rem;
}

@theme inline {
  --color-background: hsl(var(--background));
  --color-foreground: hsl(var(--foreground));
  --color-card: hsl(var(--card));
  --color-card-foreground: hsl(var(--card-foreground));
  --color-popover: hsl(var(--popover));
  --color-popover-foreground: hsl(var(--popover-foreground));
  --color-primary: hsl(var(--primary));
  --color-primary-foreground: hsl(var(--primary-foreground));
  --color-secondary: hsl(var(--secondary));
  --color-secondary-foreground: hsl(var(--secondary-foreground));
  --color-muted: hsl(var(--muted));
  --color-muted-foreground: hsl(var(--muted-foreground));
  --color-accent: hsl(var(--accent));
  --color-accent-foreground: hsl(var(--accent-foreground));
  --color-destructive: hsl(var(--destructive));
  --color-destructive-foreground: hsl(var(--destructive-foreground));
  --color-border: hsl(var(--border));
  --color-input: hsl(var(--input));
  --color-ring: hsl(var(--ring));
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
  --radius: var(--radius);
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: 222.2 84% 4.9%; /* #020817 */
    --foreground: 210 40% 98%; /* #F8FAFC */
    --card: 222.2 84% 4.9%; /* #020817 */
    --card-foreground: 210 40% 98%; /* #F8FAFC */
    --popover: 222.2 84% 4.9%; /* #020817 */
    --popover-foreground: 210 40% 98%; /* #F8FAFC */
    --primary: 217.2 91.2% 59.8%; /* #60A5FA */
    --primary-foreground: 222.2 84% 4.9%; /* #020817 */
    --secondary: 217.2 32.6% 17.5%; /* #1E293B */
    --secondary-foreground: 210 40% 98%; /* #F8FAFC */
    --muted: 217.2 32.6% 17.5%; /* #1E293B */
    --muted-foreground: 215 20.2% 65.1%; /* #94A3B8 */
    --accent: 217.2 32.6% 17.5%; /* #1E293B */
    --accent-foreground: 210 40% 98%; /* #F8FAFC */
    --destructive: 0 62.8% 30.6%; /* #DC2626 */
    --destructive-foreground: 210 40% 98%; /* #F8FAFC */
    --border: 217.2 32.6% 17.5%; /* #1E293B */
    --input: 217.2 32.6% 17.5%; /* #1E293B */
    --ring: 217.2 91.2% 59.8%; /* #60A5FA */
  }
}

body {
  background: hsl(var(--background));
  color: hsl(var(--foreground));
  font-family: var(--font-sans), Arial, Helvetica, sans-serif;
}

* {
  border-color: hsl(var(--border));
}
